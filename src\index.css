
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 0 0% 0%;  /* Pure black text */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;  /* Pure black text */

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;  /* Pure black text */

    --primary: 0 72% 51%;  /* Red color */
    --primary-foreground: 0 0% 100%;  /* White text on red background */

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 0 0% 0%;  /* Pure black text */

    --muted: 210 40% 96.1%;
    --muted-foreground: 0 0% 25%;  /* Dark gray text */

    --accent: 210 40% 96.1%;
    --accent-foreground: 0 0% 0%;  /* Pure black text */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;  /* White text on red background */

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 72% 51%;  /* Red color for focus rings */

    --radius: 0.75rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 0%;  /* Pure black text */
    --sidebar-primary: 0 72% 51%;  /* Red color */
    --sidebar-primary-foreground: 0 0% 100%;  /* White text on red background */
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 0 0% 0%;  /* Pure black text */
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 0 72% 51%;  /* Red color */
  }

  .light {
    --background: 210 40% 98%;
    --foreground: 0 0% 0%;  /* Pure black text */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;  /* Pure black text */

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;  /* Pure black text */

    --primary: 0 72% 51%;
    --primary-foreground: 0 0% 100%;  /* White text on red background */

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 0 0% 0%;  /* Pure black text */

    --muted: 210 40% 96.1%;
    --muted-foreground: 0 0% 25%;  /* Dark gray text */

    --accent: 210 40% 96.1%;
    --accent-foreground: 0 0% 0%;  /* Pure black text */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;  /* White text on red background */

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 72% 51%;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 0%;  /* Pure black text */
    --sidebar-primary: 0 72% 51%;
    --sidebar-primary-foreground: 0 0% 100%;  /* White text on red background */
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 0 0% 0%;  /* Pure black text */
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 0 72% 51%;
  }


}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    /* Samsung S25 Ultra optimizations */
    touch-action: manipulation;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Mobile optimizations for Samsung S25 Ultra */
  html, body {
    touch-action: manipulation;
    overscroll-behavior: none;
    scrollbar-width: none; /* Firefox */
    /* Optimized for 6.8" display */
    font-size: 16px;
  }
  
  html::-webkit-scrollbar, body::-webkit-scrollbar {
    display: none; /* Chrome, Safari, newer versions of Opera */
  }
}

/* Samsung S25 Ultra specific optimizations */
@media (min-width: 400px) and (max-width: 500px) and (min-height: 800px) {
  /* Large mobile display optimizations */
  .responsive-container {
    padding-left: 1.5rem; /* px-6 */
    padding-right: 1.5rem; /* px-6 */
    padding-top: 2rem; /* py-8 */
    padding-bottom: 2rem; /* py-8 */
  }

  .responsive-card {
    border-radius: 1.5rem; /* rounded-3xl */
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); /* shadow-2xl */
    padding: 2rem; /* p-8 */
  }

  .responsive-text {
    font-size: 1.125rem; /* text-lg */
    line-height: 1.75rem;
  }

  .responsive-heading {
    font-size: 1.875rem; /* text-3xl */
    line-height: 2.25rem;
    font-weight: 700; /* font-bold */
  }

  /* Touch targets optimized for S25 Ultra */
  button, input, select, textarea, [role="button"] {
    min-height: 56px;
    min-width: 56px;
  }

  /* Tab buttons larger on big mobile screens */
  [data-state="active"], [data-state="inactive"] {
    padding-top: 1.25rem; /* py-5 */
    padding-bottom: 1.25rem; /* py-5 */
    padding-left: 2rem; /* px-8 */
    padding-right: 2rem; /* px-8 */
    font-size: 1.25rem; /* text-xl */
    line-height: 1.75rem;
  }
}

/* Improved scrollbar hiding for all elements */
.scrollbar-none {
  -ms-overflow-style: none; /* Internet Explorer and Edge */
  scrollbar-width: none; /* Firefox */
  scroll-behavior: smooth;
}

.scrollbar-none::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none; /* Safari and Chrome */
}

/* Enhanced Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideFromRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideFromLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced Animation utility classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.4s ease-out;
}

.animate-slide-from-right {
  animation: slideFromRight 0.3s ease-out;
}

.animate-slide-from-left {
  animation: slideFromLeft 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 4s infinite;
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.03);
}

.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-section.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Glassmorphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}



/* Enhanced touch and haptic feedback */
.touch-feedback {
  transition: all 0.15s ease;
  -webkit-tap-highlight-color: rgba(239, 68, 68, 0.1);
}

.touch-feedback:active {
  transform: scale(0.96);
  opacity: 0.85;
}

.touch-feedback:hover {
  transform: scale(1.02);
}

/* Haptic feedback classes for different interaction types */
.haptic-light {
  transition: all 0.1s ease;
}

.haptic-medium {
  transition: all 0.15s ease;
}

.haptic-heavy {
  transition: all 0.2s ease;
}

/* Improved status colors for visits */
.status-na {
  background-color: rgb(243 244 246); /* bg-gray-100 */
  color: rgb(55 65 81); /* text-gray-700 */
  border-color: rgb(209 213 219); /* border-gray-300 */
}

.status-termin {
  background-color: rgb(254 249 195); /* bg-yellow-100 */
  color: rgb(133 77 14); /* text-yellow-800 */
  border-color: rgb(253 224 71); /* border-yellow-300 */
}

.status-kein-interesse {
  background-color: rgb(254 226 226); /* bg-red-100 */
  color: rgb(153 27 27); /* text-red-800 */
  border-color: rgb(252 165 165); /* border-red-300 */
}

.status-sale {
  background-color: rgb(220 252 231); /* bg-green-100 */
  color: rgb(22 101 52); /* text-green-800 */
  border-color: rgb(134 239 172); /* border-green-300 */
}

/* Enhanced input focus effects */
.input-focus-glow:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1),
              0 0 20px rgba(239, 68, 68, 0.2);
}

/* Force proper text colors for all input fields */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea,
select {
  color: #111827 !important; /* Force dark text in light theme */
}

/* Additional specificity for Radix UI components */
[data-radix-select-trigger] {
  color: #111827 !important;
}

/* Ensure placeholder text is visible but lighter */
input::placeholder,
textarea::placeholder {
  color: #6b7280 !important; /* Gray-500 for light theme */
  opacity: 1;
}

/* Command component input */
[cmdk-input] {
  color: #111827 !important;
}

/* OTP Input components */
[data-input-otp] input,
[data-input-otp] div {
  color: #111827 !important;
}

/* Additional coverage for any missed input elements */
.input,
[role="textbox"],
[contenteditable="true"] {
  color: #111827 !important;
}

/* Ensure all form controls have proper text color */
form input,
form textarea,
form select {
  color: #111827 !important;
}

/* Better loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Progressive enhancement for better performance */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-in,
  .animate-scale-in,
  .hover-scale,
  .hover-lift {
    animation: none;
    transition: none;
  }
}

/* Enhanced mobile interactions - Mobile-First Approach */
@media (max-width: 768px) {
  /* Ensure all touch targets meet 44px minimum */
  button, input, select, textarea, [role="button"], [role="tab"], .touch-target {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* Comfortable touch targets for primary actions */
  .primary-touch, .btn-primary, [data-primary="true"] {
    min-height: 48px;
    min-width: 48px;
  }

  /* Large touch targets for critical actions */
  .large-touch, .btn-large, [data-size="large"] {
    min-height: 56px;
    min-width: 56px;
  }

  /* Better spacing for mobile */
  .mobile-spacing > * + * {
    margin-top: 1rem; /* space-y-4 */
  }

  /* Optimized typography for mobile readability */
  .mobile-text {
    font-size: clamp(16px, 4vw, 18px);
    line-height: 1.6;
  }

  /* Mobile-optimized forms */
  .mobile-input {
    height: 3rem; /* h-12 */
    font-size: 16px; /* text-base + prevents zoom on iOS */
    border-radius: 0.75rem; /* rounded-xl */
    padding-left: 1rem; /* px-4 */
    padding-right: 1rem; /* px-4 */
  }

  /* Enhanced mobile buttons */
  .mobile-button {
    font-size: 1rem; /* text-base */
    font-weight: 600; /* font-semibold */
    border-radius: 0.75rem; /* rounded-xl */
    padding-left: 1.5rem; /* px-6 */
    padding-right: 1.5rem; /* px-6 */
    min-height: 48px; /* Increased for better touch experience */
    min-width: 48px;
    height: auto;
    padding: 12px 24px;
  }

  /* Mobile-first containers */
  .mobile-container {
    padding-left: 1rem; /* px-4 */
    padding-right: 1rem; /* px-4 */
    padding-top: 0.5rem; /* py-2 */
    padding-bottom: 0.5rem; /* py-2 */
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Mobile-optimized cards */
  .mobile-card {
    border-radius: 1rem; /* rounded-2xl */
    padding: 1rem; /* p-4 */
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); /* shadow-lg */
    margin: 0.5rem 0;
  }

  /* Swipe-friendly elements */
  .swipe-container {
    touch-action: pan-x pan-y;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile navigation optimizations */
  .mobile-nav {
    position: fixed; /* fixed */
    bottom: 0; /* bottom-0 */
    left: 0; /* left-0 */
    right: 0; /* right-0 */
    background-color: rgb(255 255 255); /* bg-white */
    border-top-width: 1px; /* border-t */
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); /* shadow-lg */
    height: 60px;
    z-index: 50;
  }

  /* Mobile-optimized tabs */
  .mobile-tabs {
    height: 5rem; /* h-20 */
    gap: 0.5rem; /* gap-2 */
    padding: 0.5rem; /* p-2 */
    min-height: 80px;
  }

  /* 2-column tabs (EFH/MFH) */
  .mobile-tabs.grid-cols-2 {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  /* 3-column tabs (KIP/TV/Mobile) */
  .mobile-tabs.grid-cols-3 {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .mobile-tab {
    height: 4rem; /* h-16 */
    font-size: 0.875rem; /* text-sm */
    font-weight: 500; /* font-medium */
    border-radius: 0.75rem; /* rounded-xl */
    min-height: 56px; /* Larger for better touch experience */
    min-width: 56px;
    padding: 12px 8px; /* Reduced horizontal padding for 3-column layout */
  }

  /* Larger padding for 2-column tabs */
  .grid-cols-2 .mobile-tab {
    padding: 12px 16px;
  }

  /* Mobile form optimizations */
  .mobile-form {
    padding: 1rem; /* p-4 */
  }

  .mobile-form > * + * {
    margin-top: 1rem; /* space-y-4 */
  }

  .mobile-form-group > * + * {
    margin-top: 0.5rem; /* space-y-2 */
  }

  .mobile-label {
    font-size: 0.875rem; /* text-sm */
    font-weight: 500; /* font-medium */
    color: rgb(55 65 81); /* text-gray-700 */
  }

  /* Mobile dropdown optimizations */
  select.mobile-select {
    height: 3rem; /* h-12 */
    font-size: 16px; /* text-base + prevents zoom on iOS */
    border-radius: 0.75rem; /* rounded-xl */
    padding-left: 1rem; /* px-4 */
    padding-right: 1rem; /* px-4 */
    background-color: rgb(255 255 255); /* bg-white */
    border-width: 2px; /* border-2 */
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  /* Mobile list optimizations */
  .mobile-list-item {
    padding: 1rem; /* p-4 */
    border-bottom-width: 1px; /* border-b */
    border-bottom-color: rgb(243 244 246); /* border-gray-100 */
    min-height: 60px;
  }

  /* Mobile action buttons */
  .mobile-action-grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.75rem; /* gap-3 */
    padding: 1rem; /* p-4 */
  }

  .mobile-action-button {
    height: 4rem; /* h-16 */
    font-size: 1rem; /* text-base */
    font-weight: 600; /* font-semibold */
    border-radius: 1rem; /* rounded-2xl */
    min-height: 56px;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-container {
    padding-left: 1.5rem; /* px-6 */
    padding-right: 1.5rem; /* px-6 */
    padding-top: 1rem; /* py-4 */
    padding-bottom: 1rem; /* py-4 */
  }

  .tablet-card {
    border-radius: 1.5rem; /* rounded-3xl */
    padding: 1.5rem; /* p-6 */
  }

  /* Larger touch targets for tablet */
  button, input, select, textarea, [role="button"], [role="tab"] {
    min-height: 48px;
    min-width: 48px;
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  .desktop-container {
    padding-left: 2rem; /* px-8 */
    padding-right: 2rem; /* px-8 */
    padding-top: 1.5rem; /* py-6 */
    padding-bottom: 1.5rem; /* py-6 */
  }

  .desktop-card {
    border-radius: 1.5rem; /* rounded-3xl */
    padding: 2rem; /* p-8 */
  }

  /* Standard touch targets for desktop */
  button, input, select, textarea, [role="button"], [role="tab"] {
    min-height: 40px;
    min-width: 40px;
  }
}

/* Modern focus indicators for accessibility */
*:focus-visible {
  outline: 2px solid #ef4444;
  outline-offset: 2px;
}

/* Remove default focus for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}



/* Theme transition for all elements */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
